import 'package:flutter/material.dart';
import 'dart:async';

class SlideshowBannerWidget extends StatefulWidget {
  const SlideshowBannerWidget({super.key});

  @override
  State<SlideshowBannerWidget> createState() => _SlideshowBannerWidgetState();
}

class _SlideshowBannerWidgetState extends State<SlideshowBannerWidget> {
  late PageController _pageController;
  late Timer _timer;
  int _currentPage = 0;

  final List<BannerItem> _bannerItems = [
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=1'),
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=2'),
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=3'),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
    _startAutoSlide();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_pageController.hasClients) {
        _currentPage = (_currentPage + 1) % _bannerItems.length;
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          SizedBox(
            height: 200,
            child: PageView.builder(
              controller: _pageController,
              padEnds: false,
              clipBehavior: Clip.none,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index % _bannerItems.length;
                });
              },
              itemBuilder: (context, index) {
                final item = _bannerItems[index % _bannerItems.length];
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Card(
                    elevation: 8,
                    shadowColor: Colors.black26,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16.0),
                      child: _BannerItemWidget(item: item),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 12),
          // Page indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _bannerItems.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: _currentPage == index ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color:
                      _currentPage == index
                          ? Theme.of(context).primaryColor
                          : Colors.grey.withValues(alpha: 0.4),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _BannerItemWidget extends StatelessWidget {
  final BannerItem item;

  const _BannerItemWidget({required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Image.network(
            item.imageUrl,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to a colored container if image fails to load
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.blue.shade400, Colors.purple.shade400],
                  ),
                ),
                child: const Icon(Icons.image, size: 50, color: Colors.white54),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.grey.shade300, Colors.grey.shade400],
                  ),
                ),
                child: const Center(child: CircularProgressIndicator()),
              );
            },
          ),
          // Overlay gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: .4),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BannerItem {
  final String imageUrl;

  BannerItem({required this.imageUrl});
}
